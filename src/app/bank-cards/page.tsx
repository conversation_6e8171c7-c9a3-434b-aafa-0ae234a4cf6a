'use client';

import React, { useState, useEffect, useCallback } from 'react';
import BankCardList from '../../components/BankCardList';
import BankCardForm from '../../components/BankCardForm';
import { BankCard } from '../../types/bank';
import { getBankCards, addBankCard, updateBankCard, deleteBankCard } from '../../services/bankCardService';
import { useAuth } from '../../contexts/AuthContext'; // Import useAuth
import styles from './page.module.css';

const BankCardsPage: React.FC = () => {
  const [cards, setCards] = useState<BankCard[]>([]);
  const [editingCard, setEditingCard] = useState<BankCard | undefined>(undefined);
  const [showForm, setShowForm] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { token } = useAuth(); // Get token from AuthContext

  const fetchCards = useCallback(async () => {
    setLoading(true);
    setError(null);
    if (!token) {
      setError('توکن احراز هویت یافت نشد. لطفا وارد شوید.');
      setLoading(false);
      return;
    }
    try {
      const fetchedCards = await getBankCards(token);
      setCards(fetchedCards);
    } catch (err) {
      console.error('Failed to fetch bank cards:', err);
      setError('خطا در بارگذاری کارت‌های بانکی. لطفا دوباره تلاش کنید.');
    } finally {
      setLoading(false);
    }
  }, [token]); // Add token to useCallback dependencies

  useEffect(() => {
    fetchCards();
  }, [fetchCards]);

  const handleAddOrUpdateCard = async (card: BankCard) => {
    setError(null);
    if (!token) {
      setError('توکن احراز هویت یافت نشد. لطفا وارد شوید.');
      return;
    }
    try {
      if ('id' in card && card.id) {
        await updateBankCard(card as BankCard, token);
      } else {
        await addBankCard(card as Omit<BankCard, 'id'>, token);
      }
      setShowForm(false);
      setEditingCard(undefined);
      fetchCards(); // Re-fetch cards to update the list
    } catch (err) {
      console.error('Failed to save bank card:', err);
      setError('خطا در ذخیره کارت بانکی. لطفا دوباره تلاش کنید.');
    }
  };

  const handleDeleteCard = async (id: string) => {
    setError(null);
    if (!token) {
      setError('توکن احراز هویت یافت نشد. لطفا وارد شوید.');
      return;
    }
    if (window.confirm('آیا از حذف این کارت بانکی اطمینان دارید؟')) {
      try {
        await deleteBankCard(id, token);
        fetchCards(); // Re-fetch cards to update the list
      } catch (err) {
        console.error('Failed to delete bank card:', err);
        setError('خطا در حذف کارت بانکی. لطفا دوباره تلاش کنید.');
      }
    }
  };

  const handleEditClick = (card: BankCard) => {
    setEditingCard(card);
    setShowForm(true);
  };

  const handleCancelForm = () => {
    setShowForm(false);
    setEditingCard(undefined);
  };

  if (loading) {
    return <div className={styles.loading}>در حال بارگذاری کارت‌های بانکی...</div>;
  }

  return (
    <div className={styles.container}>
      <h1>مدیریت کارت‌های بانکی</h1>
      {error && <p className={styles.error}>{error}</p>}
      {!showForm && (
        <button onClick={() => setShowForm(true)} className={styles.addButton}>
          افزودن کارت جدید
        </button>
      )}
      {showForm ? (
        <BankCardForm card={editingCard} onSubmit={handleAddOrUpdateCard} onCancel={handleCancelForm} />
      ) : (
        <BankCardList cards={cards} onEdit={handleEditClick} onDelete={handleDeleteCard} />
      )}
    </div>
  );
};

export default BankCardsPage;
