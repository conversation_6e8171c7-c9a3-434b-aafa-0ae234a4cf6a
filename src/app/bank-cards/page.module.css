.container {
  padding: 2rem;
  max-width: 1000px;
  margin: 0 auto;
  background-color: var(--color-background);
  min-height: 100vh;
  color: var(--color-text);
}

.container h1 {
  color: var(--color-primary);
  text-align: center;
  margin-bottom: 2rem;
  font-size: 2.5rem;
}

.loading,
.error {
  text-align: center;
  padding: 1rem;
  margin-top: 2rem;
  border-radius: 8px;
  font-weight: bold;
}

.loading {
  background-color: var(--color-background-light);
  color: var(--color-primary);
}

.error {
  background-color: var(--color-error-light);
  color: var(--color-error);
  border: 1px solid var(--color-error);
}

.addButton {
  display: block;
  margin: 1.5rem auto;
  padding: 0.8rem 2rem;
  background-color: var(--color-accent);
  color: var(--color-button-text);
  border: none;
  border-radius: 5px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.addButton:hover {
  background-color: var(--color-accent-dark);
}
