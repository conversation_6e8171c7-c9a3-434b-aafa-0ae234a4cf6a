'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import ProfileForm from '@/components/ProfileForm';
import styles from './page.module.css';
import { ProfileService } from '@/services/profileService';
import { UserProfile } from '@/types/auth';

export default function ProfilePage() {
  const { isAuthenticated, isLoading, token } = useAuth();
  const router = useRouter();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [profileLoading, setProfileLoading] = useState(true);
  const [profileError, setProfileError] = useState<string | null>(null);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, isLoading, router]);

  useEffect(() => {
    const fetchProfile = async () => {
      if (isAuthenticated && token) {
        try {
          setProfileLoading(true);
          setProfileError(null);
          const response = await ProfileService.getProfile(token);
          if (response.success && response.data) {
            setUserProfile(response.data);
          } else {
            setProfileError(response.message || 'Failed to fetch profile data.');
          }
        } catch (error: any) {
          setProfileError(error.message || 'An unexpected error occurred.');
        } finally {
          setProfileLoading(false);
        }
      }
    };

    fetchProfile();
  }, [isAuthenticated, token]);

  if (isLoading || profileLoading) {
    return (
      <div className={styles.page}>
        <div className={styles.loading}>در حال بارگذاری...</div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect to login
  }

  return (
    <div className={styles.page}>
      <div className={styles.hero}>
        <h1 className={styles.title}>پروفایل کاربری</h1>
        <p className={styles.subtitle}>
          اطلاعات شخصی خود را تکمیل و به‌روزرسانی کنید
        </p>
        {profileError && <p className={styles.error}>{profileError}</p>}
        {userProfile && (
          <p className={styles.approvalStatus}>
            وضعیت تایید: {userProfile.approvalStatus === 'pending' ? 'در انتظار' :
                          userProfile.approvalStatus === 'approved' ? 'تایید شده' :
                          userProfile.approvalStatus === 'rejected' ? 'رد شده' :
                          'ارسال نشده'}
          </p>
        )}
      </div>
      <ProfileForm />
    </div>
  );
}
