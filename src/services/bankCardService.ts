import { BankCard } from '../types/bank';
import { createApiInstance, API_CONFIG } from '../config/api';

export const getBankCards = async (token: string): Promise<BankCard[]> => {
  const api = createApiInstance(token);
  const response = await api.get<BankCard[]>(API_CONFIG.endpoints.bankCards);
  return response.data.data;
};

export const addBankCard = async (card: Omit<BankCard, 'id'>, token: string): Promise<BankCard> => {
  const api = createApiInstance(token);
  const response = await api.post<BankCard>(API_CONFIG.endpoints.bankCards, card);
  return response.data;
};

export const updateBankCard = async (card: BankCard & { id: string }, token: string): Promise<BankCard> => {
  const api = createApiInstance(token);
  // Exclude id from body
  const { id, ...cardData } = card;
  const response = await api.patch<BankCard>(`${API_CONFIG.endpoints.bankCards}/${id}`, cardData);
  return response.data;
};

export const deleteBankCard = async (id: string, token: string): Promise<void> => {
  const api = createApiInstance(token);
  await api.delete(`${API_CONFIG.endpoints.bankCards}/${id}`);
};
