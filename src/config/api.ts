import axios from 'axios';

// API Configuration
export const API_CONFIG = {
  baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL,
  endpoints: {
    currencies: '/currencies',
    sendOtp: '/users/send-otp',
    verifyOtp: '/users/verify-otp',
    user: '/users/',
    profile: '/users/profile/',
    bankCards: '/cards', // Added bankCards endpoint
  },
} as const;

// Helper function to build full API URLs
export const getApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.baseUrl}${endpoint}`;
};

export const createApiInstance = (token?: string) => {
  const api = axios.create({
    baseURL: API_CONFIG.baseUrl,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Add a request interceptor to include the auth token
  api.interceptors.request.use(
    (config) => {
      if (token) {
        config.headers = config.headers || {};
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  return api;
};

export default createApiInstance();
