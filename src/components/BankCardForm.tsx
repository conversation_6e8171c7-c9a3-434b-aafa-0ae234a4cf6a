import React, { useState, useEffect } from 'react';
import { BankCard } from '../types/bank';
import styles from './BankCardForm.module.css';

interface BankCardFormProps {
  card?: BankCard;
  onSubmit: (card: Omit<BankCard, 'id'> | BankCard) => void;
  onCancel: () => void;
}

const BankCardForm: React.FC<BankCardFormProps> = ({ card, onSubmit, onCancel }) => {
  const [cardNumber, setCardNumber] = useState('');
  const [bankName, setBankName] = useState('');
  const [accountHolderName, setAccountHolderName] = useState('');
  const [iban, setIban] = useState('');

  useEffect(() => {
    if (card) {
      setCardNumber(card.cardNumber);
      setBankName(card.bankName);
      setAccountHolderName(card.accountHolderName);
      setIban(card.iban);
    } else {
      setCardNumber('');
      setBankName('');
      setAccountHolderName('');
      setIban('');
    }
  }, [card]);

  const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/-/g, ''); // Remove dashes
    setCardNumber(value);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const newCard = { cardNumber, bankName, accountHolderName, iban };
    if (card) {
      onSubmit({ ...newCard, id: card.id });
    } else {
      onSubmit(newCard);
    }
  };

  return (
    <form onSubmit={handleSubmit} className={styles.form}>
      <h2>{card ? 'ویرایش کارت بانکی' : 'افزودن کارت بانکی جدید'}</h2>
      <div className={styles.formGroup}>
        <label htmlFor="cardNumber">شماره کارت</label>
        <input
          type="text"
          id="cardNumber"
          value={cardNumber}
          onChange={handleCardNumberChange}
          required
        />
      </div>
      <div className={styles.formGroup}>
        <label htmlFor="bankName">نام بانک</label>
        <input
          type="text"
          id="bankName"
          value={bankName}
          onChange={(e) => setBankName(e.target.value)}
          required
        />
      </div>
      <div className={styles.formGroup}>
        <label htmlFor="accountHolderName">نام صاحب حساب</label>
        <input
          type="text"
          id="accountHolderName"
          value={accountHolderName}
          onChange={(e) => setAccountHolderName(e.target.value)}
          required
        />
      </div>
      <div className={styles.formGroup}>
        <label htmlFor="iban">شماره شبا</label>
        <input
          type="text"
          id="iban"
          value={iban}
          onChange={(e) => setIban(e.target.value)}
          required
        />
      </div>
      <div className={styles.actions}>
        <button type="submit" className={styles.submitButton}>
          {card ? 'بروزرسانی کارت' : 'افزودن کارت'}
        </button>
        <button type="button" onClick={onCancel} className={styles.cancelButton}>
          لغو
        </button>
      </div>
    </form>
  );
};

export default BankCardForm;
