import React from 'react';
import { BankCard } from '../types/bank';
import styles from './BankCardList.module.css';

interface BankCardListProps {
  cards: BankCard[];
  onEdit: (card: BankCard) => void;
  onDelete: (id: string) => void;
}

const BankCardList: React.FC<BankCardListProps> = ({ cards, onEdit, onDelete }) => {
  return (
    <div className={styles.cardListContainer}>
      <h2>کارت‌های بانکی شما</h2>
      {cards.length === 0 ? (
        <p className={styles.noCardsMessage}>هنوز کارت بانکی اضافه نشده است.</p>
      ) : (
        <ul className={styles.cardList}>
          {cards.map((card) => (
            <li key={card.id} className={styles.cardItem}>
              <div className={styles.cardDetails}>
                <p><strong>نام بانک:</strong> {card.bankName}</p>
                <p><strong>شماره کارت:</strong> {card.cardNumber}</p>
                <p><strong>نام صاحب حساب:</strong> {card.accountHolderName}</p>
                <p><strong>شماره شبا:</strong> {card.iban}</p>
              </div>
              <div className={styles.cardActions}>
                <button onClick={() => onEdit(card)} className={styles.editButton}>
                  ویرایش
                </button>
                <button onClick={() => onDelete(card.id)} className={styles.deleteButton}>
                  حذف
                </button>
              </div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default BankCardList;
