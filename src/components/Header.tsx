'use client';

import { useAuth } from '@/contexts/AuthContext';
import styles from './Header.module.css';

export default function Header() {
  const { isAuthenticated, user, logout, isLoading } = useAuth();

  const handleLogout = () => {
    logout();
  };

  return (
    <header className={styles.header}>
      <div className={styles.container}>
        <div className={styles.logo}>
          <h1>صرافی</h1>
        </div>
        <nav className={styles.nav}>
          <ul>
            <li><a href="/">خانه</a></li>
            <li><a href="/about">درباره ما</a></li>
            <li><a href="/contact">تماس با ما</a></li>
            <li><a href="/bank-cards">کارت‌های بانکی</a></li>
          </ul>
        </nav>
        <div className={styles.authSection}>
          {!isLoading && (
            <>
              {isAuthenticated ? (
                <div className={styles.userMenu}>
                  <a href="/profile" className={styles.profileLink}>
                    پروفایل
                  </a>
                  <a href="/bank-cards" className={styles.profileLink}>
                    کارت‌های بانکی
                  </a>
                  <span className={styles.userInfo}>
                    {user?.mobileNumber}
                  </span>
                  <button
                    onClick={handleLogout}
                    className={styles.logoutBtn}
                  >
                    خروج
                  </button>
                </div>
              ) : (
                <a href="/login" className={styles.loginBtn}>
                  ورود
                </a>
              )}
            </>
          )}
        </div>
      </div>
    </header>
  );
}
