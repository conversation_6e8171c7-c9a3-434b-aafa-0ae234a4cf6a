.form {
  background-color: var(--color-background-light);
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  margin: 2rem auto;
}

.form h2 {
  color: var(--color-primary);
  margin-bottom: 1.5rem;
  text-align: center;
}

.formGroup {
  margin-bottom: 1rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--color-text);
  font-weight: bold;
}

.formGroup input {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background-color: var(--color-input-background);
  color: var(--color-text);
  font-size: 1rem;
}

.formGroup input:focus {
  border-color: var(--color-primary);
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.2);
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.submitButton,
.cancelButton {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  transition: background-color 0.3s ease;
}

.submitButton {
  background-color: var(--color-primary);
  color: var(--color-button-text);
}

.submitButton:hover {
  background-color: var(--color-primary-dark);
}

.cancelButton {
  background-color: var(--color-secondary);
  color: var(--color-button-text);
}

.cancelButton:hover {
  background-color: var(--color-secondary-dark);
}
