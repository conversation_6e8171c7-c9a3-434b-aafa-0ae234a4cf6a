.cardListContainer {
  background-color: var(--color-background-light);
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  margin: 2rem auto;
}

.cardListContainer h2 {
  color: var(--color-primary);
  margin-bottom: 1.5rem;
  text-align: center;
}

.noCardsMessage {
  text-align: center;
  color: var(--color-text-secondary);
  font-style: italic;
  padding: 1rem;
  border: 1px dashed var(--color-border);
  border-radius: 4px;
}

.cardList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.cardItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--color-card-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease-in-out;
}

.cardItem:hover {
  transform: translateY(-3px);
}

.cardDetails p {
  margin: 0.5rem 0;
  color: var(--color-text);
}

.cardDetails p strong {
  color: var(--color-primary);
}

.cardActions {
  display: flex;
  gap: 0.8rem;
}

.editButton,
.deleteButton {
  padding: 0.6rem 1.2rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: bold;
  transition: background-color 0.3s ease;
}

.editButton {
  background-color: var(--color-accent);
  color: var(--color-button-text);
}

.editButton:hover {
  background-color: var(--color-accent-dark);
}

.deleteButton {
  background-color: var(--color-error);
  color: var(--color-button-text);
}

.deleteButton:hover {
  background-color: var(--color-error-dark);
}
